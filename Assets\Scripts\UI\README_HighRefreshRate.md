# 高刷新率系统使用说明

## 概述

本系统为Unity赛车游戏项目实现了120fps高刷新率功能，主要在主菜单页面进行初始化和管理。

## 主要组件

### 1. MainMenuUIManager (修改)
- **位置**: `Assets/Scripts/UI/Core/UIManager.cs`
- **功能**: 主菜单UI管理器，现已集成高刷新率设置功能
- **新增功能**:
  - 120fps高刷新率初始化
  - 移动平台和PC平台适配
  - 设置保存和加载
  - VSync控制

### 2. HighRefreshRateDisplay (新增)
- **位置**: `Assets/Scripts/UI/HighRefreshRateDisplay.cs`
- **功能**: 实时显示帧率信息和设置状态
- **特性**:
  - 实时FPS显示
  - 帧率统计（平均、最小、最大）
  - 设置信息显示
  - 颜色编码（绿色=好，黄色=中等，红色=差）

### 3. HighRefreshRateTester (新增)
- **位置**: `Assets/Scripts/Editor/HighRefreshRateTester.cs`
- **功能**: 编辑器测试工具
- **访问**: Unity菜单 → Tools → 高刷新率测试工具

## 使用方法

### 在主菜单场景中设置

1. **在MainMenuUIManager GameObject上**:
   - 确保已附加 `MainMenuUIManager` 脚本
   - 在Inspector中配置高刷新率设置：
     - `Enable High Refresh Rate`: 是否启用120fps
     - `Target Frame Rate`: 目标帧率（默认120）
     - `Show Frame Rate Info`: 是否显示帧率信息

2. **添加帧率显示组件（可选）**:
   - 创建一个UI Text对象
   - 附加 `HighRefreshRateDisplay` 脚本
   - 将此对象设置为MainMenuUIManager的子对象
   - 在MainMenuUIManager的Inspector中分配 `High Refresh Rate Display` 字段

### 代码调用示例

```csharp
// 启用/禁用高刷新率
MainMenuUIManager.Instance.SetHighRefreshRateEnabled(true);

// 设置目标帧率
MainMenuUIManager.Instance.SetTargetFrameRate(120);

// 显示/隐藏帧率信息
MainMenuUIManager.Instance.SetShowFrameRateInfo(true);

// 获取当前设置
bool isEnabled = MainMenuUIManager.Instance.IsHighRefreshRateEnabled();
int targetFPS = MainMenuUIManager.Instance.GetTargetFrameRate();
```

## 技术特性

### 平台适配
- **PC平台**: 直接设置目标帧率，禁用VSync
- **移动平台**: 检查设备支持的最大刷新率，智能适配

### 设置持久化
- 使用PlayerPrefs保存用户设置
- 支持以下设置项：
  - `EnableHighRefreshRate`: 是否启用高刷新率
  - `TargetFrameRate`: 目标帧率
  - `ShowFrameRateInfo`: 是否显示帧率信息

### 性能优化
- 避免重复应用设置
- 智能VSync控制
- 帧率范围限制（30-240fps）

## 调试和测试

### 使用编辑器工具
1. 打开 `Tools → 高刷新率测试工具`
2. 查看当前状态
3. 调整设置并应用
4. 使用预设帧率快速测试

### 控制台日志
系统会输出详细的调试信息：
```
MainMenuUIManager: PC平台高刷新率已启用 - 120fps
MainMenuUIManager: 当前帧率设置 - VSync: 0, 目标帧率: 120
```

### 帧率显示
启用帧率信息显示后，屏幕上会显示：
- 当前FPS（带颜色编码）
- 平均/最小/最大FPS
- 帧时间（毫秒）
- 设置状态信息

## 注意事项

1. **编辑器测试**: 在Unity编辑器中测试时，实际帧率可能受到编辑器性能影响
2. **移动设备**: 不是所有移动设备都支持高刷新率，系统会自动适配
3. **电池消耗**: 高刷新率会增加电池消耗，建议提供用户选择
4. **性能影响**: 确保游戏内容能够稳定运行在目标帧率

## 扩展功能

### 添加到设置菜单
可以在游戏设置界面添加高刷新率选项：

```csharp
// 在设置UI中添加切换按钮
public void OnHighRefreshRateToggle(bool enabled)
{
    if (MainMenuUIManager.Instance != null)
    {
        MainMenuUIManager.Instance.SetHighRefreshRateEnabled(enabled);
    }
}

// 添加帧率选择下拉菜单
public void OnFrameRateDropdownChanged(int index)
{
    int[] frameRates = { 60, 90, 120, 144 };
    if (index < frameRates.Length)
    {
        MainMenuUIManager.Instance.SetTargetFrameRate(frameRates[index]);
    }
}
```

### 自动检测设备能力
```csharp
// 检测设备是否支持高刷新率
bool supportsHighRefreshRate = Screen.currentResolution.refreshRate >= 90;
if (supportsHighRefreshRate)
{
    // 启用高刷新率选项
}
```

## 故障排除

1. **帧率没有提升**: 检查VSync是否已禁用，确认设备支持目标帧率
2. **显示组件不工作**: 确认HighRefreshRateDisplay组件已正确附加和配置
3. **设置不保存**: 检查PlayerPrefs.Save()是否被调用
4. **移动平台问题**: 确认设备刷新率设置，某些设备需要手动启用高刷新率模式
