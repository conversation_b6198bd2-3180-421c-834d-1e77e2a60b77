using UnityEngine;
using UnityEngine.SceneManagement; // 如果需要场景加载，保留

public class MainMenuUIManager : MonoBehaviour // 类名已更改
{
    #region Singleton
    public static MainMenuUIManager Instance { get; private set; }
    #endregion

    #region 私有字段
    [Header("主菜单 UI 面板")]
    [SerializeField] private GameObject m_MainMenuPanel;
    [SerializeField] private GameObject m_MapSelectionPanel;
    [SerializeField] private GameObject m_GaragePanel;
    [SerializeField] private GameObject m_SettingsPanel; // 这是主菜单的设置面板
    // 可以根据需要添加其他主菜单相关面板

    [Header("背景音乐")]
    [SerializeField, Tooltip("主菜单界面的背景音乐片段")]
    private AudioClip m_MainMenuBGMClip;

    [Header("高刷新率设置")]
    [SerializeField, Tooltip("是否启用120fps高刷新率")]
    private bool m_EnableHighRefreshRate = true;
    [SerializeField, Tooltip("目标帧率（移动平台和支持高刷新率的设备）")]
    private int m_TargetFrameRate = 120;
    [SerializeField, Tooltip("是否在启动时显示帧率信息")]
    private bool m_ShowFrameRateInfo = false;
    [SerializeField, Tooltip("高刷新率显示组件")]
    private HighRefreshRateDisplay m_HighRefreshRateDisplay;

    // 帧率相关的私有字段
    private bool m_HighRefreshRateApplied = false;
    #endregion

    #region Unity生命周期
    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            // DontDestroyOnLoad(gameObject); // 通常主菜单UI管理器不需要跨场景保留

            // 初始化高刷新率设置
            InitializeHighRefreshRate();
        }
        else
        {
            Destroy(gameObject);
        }
    }

    private void Start()
    {
        // 初始时，通常显示主菜单，隐藏其他面板
        ShowMainMenuPanel();

        // 确保AudioManager存在
        EnsureAudioManagerExists();

        // 播放主菜单BGM
        if (AudioManager.Instance != null && m_MainMenuBGMClip != null)
        {
            AudioManager.Instance.PlayMusic(m_MainMenuBGMClip);

            // 确保应用保存的音量设置
            float savedBGMVolume = PlayerPrefs.GetFloat("BGMVolume", 0.8f);
            AudioManager.Instance.SetBGMVolume(savedBGMVolume);
            Debug.Log($"MainMenuUIManager: 应用保存的BGM音量: {savedBGMVolume}");
        }
        else
        {
            if (AudioManager.Instance == null) Debug.LogError("MainMenuUIManager: AudioManager 实例未找到，无法播放BGM！");
            if (m_MainMenuBGMClip == null) Debug.LogWarning("MainMenuUIManager: 主菜单BGM音频片段 (m_MainMenuBGMClip) 未在Inspector中分配。");
        }

        // 初始化高刷新率显示组件
        InitializeHighRefreshRateDisplay();
    }

    /// <summary>
    /// 确保AudioManager存在
    /// </summary>
    private void EnsureAudioManagerExists()
    {
        if (AudioManager.Instance != null) return;

        // 尝试查找AudioManagerInitializer
        AudioManagerInitializer initializer = FindObjectOfType<AudioManagerInitializer>();
        if (initializer != null)
        {
            initializer.EnsureAudioManagerExists();
            Debug.Log("MainMenuUIManager: 通过AudioManagerInitializer创建AudioManager");
        }
        else
        {
            Debug.LogWarning("MainMenuUIManager: 未找到AudioManagerInitializer，尝试创建临时AudioManager");

            // 创建临时AudioManager
            GameObject audioManagerObj = new GameObject("AudioManager");
            AudioManager manager = audioManagerObj.AddComponent<AudioManager>();

            // 添加AudioSource组件
            AudioSource bgmSource = audioManagerObj.AddComponent<AudioSource>();
            bgmSource.playOnAwake = false;
            bgmSource.loop = true;

            // 尝试调用Awake方法
            manager.SendMessage("Awake", null, SendMessageOptions.DontRequireReceiver);

            Debug.Log("MainMenuUIManager: 已创建临时AudioManager");
        }
    }
    #endregion

    #region 公共方法
    public void ShowMainMenuPanel()
    {
        HideAllMainMenuPanels();
        if (m_MainMenuPanel != null)
        {
            m_MainMenuPanel.SetActive(true);
        }
    }

    public void ShowMapSelectionPanel()
    {
        HideAllMainMenuPanels();
        if (m_MapSelectionPanel != null)
        {
            m_MapSelectionPanel.SetActive(true);
        }
    }

    public void ShowGaragePanel()
    {
        HideAllMainMenuPanels();
        if (m_GaragePanel != null)
        {
            m_GaragePanel.SetActive(true);
        }
    }

    public void ShowSettingsPanel() // 主菜单的设置面板
    {
        HideAllMainMenuPanels();
        if (m_SettingsPanel != null)
        {
            m_SettingsPanel.SetActive(true);
        }
    }

    public void HideAllMainMenuPanels() // 只隐藏主菜单相关面板
    {
        if (m_MainMenuPanel != null) m_MainMenuPanel.SetActive(false);
        if (m_MapSelectionPanel != null) m_MapSelectionPanel.SetActive(false);
        if (m_GaragePanel != null) m_GaragePanel.SetActive(false);
        if (m_SettingsPanel != null) m_SettingsPanel.SetActive(false);
    }
    #endregion

    #region 高刷新率设置方法
    /// <summary>
    /// 初始化高刷新率设置
    /// </summary>
    private void InitializeHighRefreshRate()
    {
        // 从PlayerPrefs加载用户设置
        bool enableHighRefreshRate = PlayerPrefs.GetInt("EnableHighRefreshRate", m_EnableHighRefreshRate ? 1 : 0) == 1;
        int targetFrameRate = PlayerPrefs.GetInt("TargetFrameRate", m_TargetFrameRate);

        // 应用高刷新率设置
        ApplyHighRefreshRateSettings(enableHighRefreshRate, targetFrameRate);

        if (m_ShowFrameRateInfo)
        {
            Debug.Log($"MainMenuUIManager: 高刷新率设置已初始化 - 启用: {enableHighRefreshRate}, 目标帧率: {targetFrameRate}");
        }
    }

    /// <summary>
    /// 应用高刷新率设置
    /// </summary>
    /// <param name="_enableHighRefreshRate">是否启用高刷新率</param>
    /// <param name="_targetFrameRate">目标帧率</param>
    private void ApplyHighRefreshRateSettings(bool _enableHighRefreshRate, int _targetFrameRate)
    {
        if (m_HighRefreshRateApplied) return; // 避免重复应用

        try
        {
            if (_enableHighRefreshRate)
            {
                // 禁用VSync以允许高帧率
                QualitySettings.vSyncCount = 0;

                // 设置目标帧率
                Application.targetFrameRate = _targetFrameRate;

                // 移动平台特殊处理
                if (Application.isMobilePlatform)
                {
                    // 确保移动设备支持高刷新率
                    if (Screen.currentResolution.refreshRate >= _targetFrameRate)
                    {
                        Application.targetFrameRate = _targetFrameRate;
                        Debug.Log($"MainMenuUIManager: 移动平台高刷新率已启用 - {_targetFrameRate}fps");
                    }
                    else
                    {
                        // 如果设备不支持目标帧率，使用设备最大刷新率
                        Application.targetFrameRate = Screen.currentResolution.refreshRate;
                        Debug.Log($"MainMenuUIManager: 移动平台使用设备最大刷新率 - {Screen.currentResolution.refreshRate}fps");
                    }
                }
                else
                {
                    // PC平台直接设置目标帧率
                    Application.targetFrameRate = _targetFrameRate;
                    Debug.Log($"MainMenuUIManager: PC平台高刷新率已启用 - {_targetFrameRate}fps");
                }
            }
            else
            {
                // 使用默认设置
                QualitySettings.vSyncCount = 1; // 启用VSync
                Application.targetFrameRate = 60; // 标准60fps
                Debug.Log("MainMenuUIManager: 使用标准60fps设置");
            }

            m_HighRefreshRateApplied = true;

            if (m_ShowFrameRateInfo)
            {
                Debug.Log($"MainMenuUIManager: 当前帧率设置 - VSync: {QualitySettings.vSyncCount}, 目标帧率: {Application.targetFrameRate}");
            }
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"MainMenuUIManager: 应用高刷新率设置时发生错误: {ex.Message}");
        }
    }

    /// <summary>
    /// 设置高刷新率开关（供设置界面调用）
    /// </summary>
    /// <param name="_enable">是否启用高刷新率</param>
    public void SetHighRefreshRateEnabled(bool _enable)
    {
        PlayerPrefs.SetInt("EnableHighRefreshRate", _enable ? 1 : 0);
        PlayerPrefs.Save();

        // 重新应用设置
        m_HighRefreshRateApplied = false;
        ApplyHighRefreshRateSettings(_enable, PlayerPrefs.GetInt("TargetFrameRate", m_TargetFrameRate));

        // 刷新显示组件
        RefreshHighRefreshRateDisplay();

        Debug.Log($"MainMenuUIManager: 高刷新率设置已更新 - 启用: {_enable}");
    }

    /// <summary>
    /// 设置目标帧率（供设置界面调用）
    /// </summary>
    /// <param name="_frameRate">目标帧率</param>
    public void SetTargetFrameRate(int _frameRate)
    {
        // 限制帧率范围
        _frameRate = Mathf.Clamp(_frameRate, 30, 240);

        PlayerPrefs.SetInt("TargetFrameRate", _frameRate);
        PlayerPrefs.Save();

        // 如果当前启用了高刷新率，重新应用设置
        bool enableHighRefreshRate = PlayerPrefs.GetInt("EnableHighRefreshRate", 1) == 1;
        if (enableHighRefreshRate)
        {
            m_HighRefreshRateApplied = false;
            ApplyHighRefreshRateSettings(true, _frameRate);
        }

        // 刷新显示组件
        RefreshHighRefreshRateDisplay();

        Debug.Log($"MainMenuUIManager: 目标帧率已设置为 {_frameRate}fps");
    }

    /// <summary>
    /// 获取当前高刷新率启用状态
    /// </summary>
    /// <returns>是否启用高刷新率</returns>
    public bool IsHighRefreshRateEnabled()
    {
        return PlayerPrefs.GetInt("EnableHighRefreshRate", m_EnableHighRefreshRate ? 1 : 0) == 1;
    }

    /// <summary>
    /// 获取当前目标帧率
    /// </summary>
    /// <returns>目标帧率</returns>
    public int GetTargetFrameRate()
    {
        return PlayerPrefs.GetInt("TargetFrameRate", m_TargetFrameRate);
    }

    /// <summary>
    /// 切换帧率信息显示
    /// </summary>
    /// <param name="_show">是否显示帧率信息</param>
    public void SetShowFrameRateInfo(bool _show)
    {
        m_ShowFrameRateInfo = _show;
        PlayerPrefs.SetInt("ShowFrameRateInfo", _show ? 1 : 0);
        PlayerPrefs.Save();

        // 更新显示组件状态
        if (m_HighRefreshRateDisplay != null)
        {
            m_HighRefreshRateDisplay.SetDisplayEnabled(_show);
        }
    }

    /// <summary>
    /// 初始化高刷新率显示组件
    /// </summary>
    private void InitializeHighRefreshRateDisplay()
    {
        if (m_HighRefreshRateDisplay == null)
        {
            // 尝试在子对象中查找HighRefreshRateDisplay组件
            m_HighRefreshRateDisplay = GetComponentInChildren<HighRefreshRateDisplay>();
        }

        if (m_HighRefreshRateDisplay != null)
        {
            // 根据保存的设置决定是否显示
            bool showFrameRateInfo = PlayerPrefs.GetInt("ShowFrameRateInfo", m_ShowFrameRateInfo ? 1 : 0) == 1;
            m_HighRefreshRateDisplay.SetDisplayEnabled(showFrameRateInfo);

            Debug.Log($"MainMenuUIManager: 高刷新率显示组件已初始化 - 显示状态: {showFrameRateInfo}");
        }
        else
        {
            if (m_ShowFrameRateInfo)
            {
                Debug.LogWarning("MainMenuUIManager: 未找到HighRefreshRateDisplay组件，但启用了帧率信息显示。");
            }
        }
    }

    /// <summary>
    /// 刷新高刷新率显示信息
    /// </summary>
    public void RefreshHighRefreshRateDisplay()
    {
        if (m_HighRefreshRateDisplay != null)
        {
            m_HighRefreshRateDisplay.RefreshSettingsDisplay();
        }
    }
    #endregion
}