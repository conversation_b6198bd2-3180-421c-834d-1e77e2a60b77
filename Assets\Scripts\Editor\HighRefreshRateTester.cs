using UnityEngine;
using UnityEditor;

/// <summary>
/// 高刷新率功能测试工具
/// 提供编辑器窗口来测试和调试高刷新率设置
/// </summary>
public class HighRefreshRateTester : EditorWindow
{
    #region 私有字段
    private bool m_EnableHighRefreshRate = true;
    private int m_TargetFrameRate = 120;
    private bool m_ShowFrameRateInfo = false;
    private Vector2 m_ScrollPosition;
    
    // 预设帧率选项
    private readonly int[] m_FrameRatePresets = { 30, 60, 90, 120, 144, 165, 240 };
    private readonly string[] m_FrameRateLabels = { "30fps", "60fps", "90fps", "120fps", "144fps", "165fps", "240fps" };
    #endregion

    #region 菜单项
    [MenuItem("Tools/高刷新率测试工具")]
    public static void ShowWindow()
    {
        HighRefreshRateTester window = GetWindow<HighRefreshRateTester>("高刷新率测试");
        window.minSize = new Vector2(400, 300);
        window.Show();
    }
    #endregion

    #region Unity编辑器方法
    private void OnEnable()
    {
        LoadCurrentSettings();
    }

    private void OnGUI()
    {
        m_ScrollPosition = EditorGUILayout.BeginScrollView(m_ScrollPosition);

        EditorGUILayout.LabelField("高刷新率设置测试工具", EditorStyles.boldLabel);
        EditorGUILayout.Space();

        DrawCurrentStatus();
        EditorGUILayout.Space();

        DrawSettings();
        EditorGUILayout.Space();

        DrawPresets();
        EditorGUILayout.Space();

        DrawActions();
        EditorGUILayout.Space();

        DrawInfo();

        EditorGUILayout.EndScrollView();
    }
    #endregion

    #region 私有方法
    /// <summary>
    /// 加载当前设置
    /// </summary>
    private void LoadCurrentSettings()
    {
        if (MainMenuUIManager.Instance != null)
        {
            m_EnableHighRefreshRate = MainMenuUIManager.Instance.IsHighRefreshRateEnabled();
            m_TargetFrameRate = MainMenuUIManager.Instance.GetTargetFrameRate();
        }
        else
        {
            m_EnableHighRefreshRate = PlayerPrefs.GetInt("EnableHighRefreshRate", 1) == 1;
            m_TargetFrameRate = PlayerPrefs.GetInt("TargetFrameRate", 120);
        }
        
        m_ShowFrameRateInfo = PlayerPrefs.GetInt("ShowFrameRateInfo", 0) == 1;
    }

    /// <summary>
    /// 绘制当前状态
    /// </summary>
    private void DrawCurrentStatus()
    {
        EditorGUILayout.LabelField("当前状态", EditorStyles.boldLabel);
        
        EditorGUI.BeginDisabledGroup(true);
        EditorGUILayout.TextField("当前目标帧率", Application.targetFrameRate.ToString());
        EditorGUILayout.TextField("VSync计数", QualitySettings.vSyncCount.ToString());
        EditorGUILayout.TextField("平台", Application.isMobilePlatform ? "移动平台" : "PC平台");
        
        if (Application.isMobilePlatform)
        {
            EditorGUILayout.TextField("设备刷新率", Screen.currentResolution.refreshRate.ToString());
        }
        EditorGUI.EndDisabledGroup();
    }

    /// <summary>
    /// 绘制设置选项
    /// </summary>
    private void DrawSettings()
    {
        EditorGUILayout.LabelField("设置选项", EditorStyles.boldLabel);
        
        m_EnableHighRefreshRate = EditorGUILayout.Toggle("启用高刷新率", m_EnableHighRefreshRate);
        
        EditorGUI.BeginDisabledGroup(!m_EnableHighRefreshRate);
        m_TargetFrameRate = EditorGUILayout.IntSlider("目标帧率", m_TargetFrameRate, 30, 240);
        EditorGUI.EndDisabledGroup();
        
        m_ShowFrameRateInfo = EditorGUILayout.Toggle("显示帧率信息", m_ShowFrameRateInfo);
    }

    /// <summary>
    /// 绘制预设选项
    /// </summary>
    private void DrawPresets()
    {
        EditorGUILayout.LabelField("帧率预设", EditorStyles.boldLabel);
        
        EditorGUILayout.BeginHorizontal();
        for (int i = 0; i < m_FrameRatePresets.Length; i++)
        {
            if (GUILayout.Button(m_FrameRateLabels[i]))
            {
                m_TargetFrameRate = m_FrameRatePresets[i];
            }
            
            if ((i + 1) % 4 == 0)
            {
                EditorGUILayout.EndHorizontal();
                EditorGUILayout.BeginHorizontal();
            }
        }
        EditorGUILayout.EndHorizontal();
    }

    /// <summary>
    /// 绘制操作按钮
    /// </summary>
    private void DrawActions()
    {
        EditorGUILayout.LabelField("操作", EditorStyles.boldLabel);
        
        EditorGUILayout.BeginHorizontal();
        
        if (GUILayout.Button("应用设置"))
        {
            ApplySettings();
        }
        
        if (GUILayout.Button("重置为默认"))
        {
            ResetToDefaults();
        }
        
        if (GUILayout.Button("刷新状态"))
        {
            LoadCurrentSettings();
        }
        
        EditorGUILayout.EndHorizontal();
        
        EditorGUILayout.Space();
        
        EditorGUILayout.BeginHorizontal();
        
        if (GUILayout.Button("测试60fps"))
        {
            TestFrameRate(60);
        }
        
        if (GUILayout.Button("测试120fps"))
        {
            TestFrameRate(120);
        }
        
        if (GUILayout.Button("恢复VSync"))
        {
            RestoreVSync();
        }
        
        EditorGUILayout.EndHorizontal();
    }

    /// <summary>
    /// 绘制信息
    /// </summary>
    private void DrawInfo()
    {
        EditorGUILayout.LabelField("说明", EditorStyles.boldLabel);
        
        EditorGUILayout.HelpBox(
            "此工具用于测试高刷新率功能。\n\n" +
            "• 应用设置：将当前设置应用到MainMenuUIManager\n" +
            "• 测试帧率：直接设置Unity的目标帧率进行测试\n" +
            "• 恢复VSync：恢复VSync设置\n\n" +
            "注意：在编辑器中测试时，实际帧率可能受到编辑器性能影响。",
            MessageType.Info
        );
    }

    /// <summary>
    /// 应用设置
    /// </summary>
    private void ApplySettings()
    {
        if (MainMenuUIManager.Instance != null)
        {
            MainMenuUIManager.Instance.SetHighRefreshRateEnabled(m_EnableHighRefreshRate);
            MainMenuUIManager.Instance.SetTargetFrameRate(m_TargetFrameRate);
            MainMenuUIManager.Instance.SetShowFrameRateInfo(m_ShowFrameRateInfo);
            
            Debug.Log($"HighRefreshRateTester: 设置已应用 - 启用: {m_EnableHighRefreshRate}, 目标帧率: {m_TargetFrameRate}fps");
        }
        else
        {
            // 直接设置PlayerPrefs
            PlayerPrefs.SetInt("EnableHighRefreshRate", m_EnableHighRefreshRate ? 1 : 0);
            PlayerPrefs.SetInt("TargetFrameRate", m_TargetFrameRate);
            PlayerPrefs.SetInt("ShowFrameRateInfo", m_ShowFrameRateInfo ? 1 : 0);
            PlayerPrefs.Save();
            
            Debug.Log("HighRefreshRateTester: MainMenuUIManager未找到，设置已保存到PlayerPrefs");
        }
    }

    /// <summary>
    /// 重置为默认设置
    /// </summary>
    private void ResetToDefaults()
    {
        m_EnableHighRefreshRate = true;
        m_TargetFrameRate = 120;
        m_ShowFrameRateInfo = false;
        
        Debug.Log("HighRefreshRateTester: 设置已重置为默认值");
    }

    /// <summary>
    /// 测试指定帧率
    /// </summary>
    /// <param name="_frameRate">目标帧率</param>
    private void TestFrameRate(int _frameRate)
    {
        QualitySettings.vSyncCount = 0;
        Application.targetFrameRate = _frameRate;
        
        Debug.Log($"HighRefreshRateTester: 测试帧率设置 - {_frameRate}fps (VSync已禁用)");
    }

    /// <summary>
    /// 恢复VSync设置
    /// </summary>
    private void RestoreVSync()
    {
        QualitySettings.vSyncCount = 1;
        Application.targetFrameRate = 60;
        
        Debug.Log("HighRefreshRateTester: VSync已恢复，目标帧率设置为60fps");
    }
    #endregion
}
