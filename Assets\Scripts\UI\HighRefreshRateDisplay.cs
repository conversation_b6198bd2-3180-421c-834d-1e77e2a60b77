using UnityEngine;
using UnityEngine.UI;
using TMPro;

/// <summary>
/// 高刷新率状态显示组件
/// 用于在主菜单显示当前的帧率设置和实时FPS信息
/// </summary>
public class HighRefreshRateDisplay : MonoBehaviour
{
    #region 私有字段
    [Header("UI组件")]
    [SerializeField, Tooltip("显示帧率信息的文本组件")]
    private TextMeshProUGUI m_FpsText;
    [SerializeField, Tooltip("显示设置信息的文本组件")]
    private TextMeshProUGUI m_SettingsText;

    [Header("显示设置")]
    [SerializeField, Tooltip("FPS更新间隔（秒）")]
    private float m_UpdateInterval = 0.5f;
    [SerializeField, Tooltip("是否显示详细信息")]
    private bool m_ShowDetailedInfo = true;
    [SerializeField, Tooltip("是否在游戏开始时自动隐藏")]
    private bool m_AutoHideOnGameStart = true;

    // 帧率计算相关
    private int m_FrameCount = 0;
    private float m_LastUpdateTime = 0f;
    private float m_CurrentFPS = 0f;
    private float m_AverageFPS = 0f;
    private float m_MinFPS = float.MaxValue;
    private float m_MaxFPS = 0f;

    // 颜色设置
    private readonly Color m_GoodFPSColor = Color.green;
    private readonly Color m_MediumFPSColor = Color.yellow;
    private readonly Color m_BadFPSColor = Color.red;
    #endregion

    #region Unity生命周期
    private void Start()
    {
        InitializeDisplay();
        UpdateSettingsDisplay();
    }

    private void Update()
    {
        UpdateFPSDisplay();
    }

    private void OnEnable()
    {
        // 重置FPS统计
        ResetFPSStats();
    }
    #endregion

    #region 私有方法
    /// <summary>
    /// 初始化显示组件
    /// </summary>
    private void InitializeDisplay()
    {
        // 如果没有指定文本组件，尝试自动查找
        if (m_FpsText == null)
        {
            m_FpsText = GetComponentInChildren<TextMeshProUGUI>();
        }

        // 设置初始状态
        m_LastUpdateTime = Time.realtimeSinceStartup;
        ResetFPSStats();

        // 检查是否应该显示
        bool showFrameRateInfo = PlayerPrefs.GetInt("ShowFrameRateInfo", 0) == 1;
        gameObject.SetActive(showFrameRateInfo);
    }

    /// <summary>
    /// 更新FPS显示
    /// </summary>
    private void UpdateFPSDisplay()
    {
        m_FrameCount++;
        float currentTime = Time.realtimeSinceStartup;

        if (currentTime >= m_LastUpdateTime + m_UpdateInterval)
        {
            // 计算当前FPS
            m_CurrentFPS = m_FrameCount / (currentTime - m_LastUpdateTime);
            
            // 更新统计信息
            if (m_CurrentFPS > m_MaxFPS) m_MaxFPS = m_CurrentFPS;
            if (m_CurrentFPS < m_MinFPS) m_MinFPS = m_CurrentFPS;
            
            // 计算平均FPS（简单移动平均）
            m_AverageFPS = (m_AverageFPS + m_CurrentFPS) * 0.5f;

            // 更新显示
            UpdateFPSText();

            // 重置计数器
            m_FrameCount = 0;
            m_LastUpdateTime = currentTime;
        }
    }

    /// <summary>
    /// 更新FPS文本显示
    /// </summary>
    private void UpdateFPSText()
    {
        if (m_FpsText == null) return;

        // 根据FPS选择颜色
        Color fpsColor = GetFPSColor(m_CurrentFPS);
        string colorHex = ColorUtility.ToHtmlStringRGB(fpsColor);

        string fpsDisplay;
        if (m_ShowDetailedInfo)
        {
            fpsDisplay = $"<color=#{colorHex}>{m_CurrentFPS:F1}</color> FPS\n" +
                        $"平均: {m_AverageFPS:F1} | 最小: {m_MinFPS:F1} | 最大: {m_MaxFPS:F1}\n" +
                        $"帧时间: {(1000f / Mathf.Max(m_CurrentFPS, 0.001f)):F1}ms";
        }
        else
        {
            fpsDisplay = $"<color=#{colorHex}>{m_CurrentFPS:F1}</color> FPS";
        }

        m_FpsText.text = fpsDisplay;
    }

    /// <summary>
    /// 更新设置信息显示
    /// </summary>
    private void UpdateSettingsDisplay()
    {
        if (m_SettingsText == null) return;

        if (MainMenuUIManager.Instance != null)
        {
            bool highRefreshEnabled = MainMenuUIManager.Instance.IsHighRefreshRateEnabled();
            int targetFrameRate = MainMenuUIManager.Instance.GetTargetFrameRate();
            
            string settingsInfo = $"高刷新率: {(highRefreshEnabled ? "启用" : "禁用")}\n" +
                                 $"目标帧率: {targetFrameRate}fps\n" +
                                 $"VSync: {(QualitySettings.vSyncCount > 0 ? "启用" : "禁用")}\n" +
                                 $"平台: {(Application.isMobilePlatform ? "移动" : "PC")}";

            m_SettingsText.text = settingsInfo;
        }
    }

    /// <summary>
    /// 根据FPS值获取对应的颜色
    /// </summary>
    /// <param name="_fps">FPS值</param>
    /// <returns>对应的颜色</returns>
    private Color GetFPSColor(float _fps)
    {
        if (_fps >= 90f) return m_GoodFPSColor;
        if (_fps >= 60f) return Color.Lerp(m_MediumFPSColor, m_GoodFPSColor, (_fps - 60f) / 30f);
        if (_fps >= 30f) return Color.Lerp(m_BadFPSColor, m_MediumFPSColor, (_fps - 30f) / 30f);
        return m_BadFPSColor;
    }

    /// <summary>
    /// 重置FPS统计信息
    /// </summary>
    private void ResetFPSStats()
    {
        m_FrameCount = 0;
        m_CurrentFPS = 0f;
        m_AverageFPS = 0f;
        m_MinFPS = float.MaxValue;
        m_MaxFPS = 0f;
        m_LastUpdateTime = Time.realtimeSinceStartup;
    }
    #endregion

    #region 公共方法
    /// <summary>
    /// 切换显示状态
    /// </summary>
    /// <param name="_show">是否显示</param>
    public void SetDisplayEnabled(bool _show)
    {
        gameObject.SetActive(_show);
        if (_show)
        {
            ResetFPSStats();
            UpdateSettingsDisplay();
        }
    }

    /// <summary>
    /// 切换详细信息显示
    /// </summary>
    /// <param name="_showDetailed">是否显示详细信息</param>
    public void SetShowDetailedInfo(bool _showDetailed)
    {
        m_ShowDetailedInfo = _showDetailed;
    }

    /// <summary>
    /// 手动刷新设置显示
    /// </summary>
    public void RefreshSettingsDisplay()
    {
        UpdateSettingsDisplay();
    }

    /// <summary>
    /// 重置统计信息
    /// </summary>
    public void ResetStats()
    {
        ResetFPSStats();
    }
    #endregion

    #region 调试方法
#if UNITY_EDITOR
    [ContextMenu("测试显示")]
    private void TestDisplay()
    {
        SetDisplayEnabled(true);
        m_ShowDetailedInfo = true;
        UpdateSettingsDisplay();
    }

    [ContextMenu("重置统计")]
    private void TestResetStats()
    {
        ResetStats();
    }
#endif
    #endregion
}
